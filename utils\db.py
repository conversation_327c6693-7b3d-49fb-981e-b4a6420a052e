import sqlite3, json
from datetime import datetime, timedelta

class DATABASE_manager:
    def __init__(self, db_name="db/clinic.db"):
        self.conn = sqlite3.connect(db_name, check_same_thread=False)
        self.create_tables()

    def create_tables(self):
        cursor = self.conn.cursor()
        cursor.execute('''CREATE TABLE IF NOT EXISTS patients (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name TEXT NOT NULL UNIQUE,
                            age INTEGER,
                            phone TEXT NOT NULL)''')

        cursor.execute('''CREATE TABLE IF NOT EXISTS services (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            name TEXT UNIQUE NOT NULL,
                            cost REAL NOT NULL)''')

        cursor.execute('''CREATE TABLE IF NOT EXISTS visits (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            patient_id INTEGER,
                            date TEXT DEFAULT CURRENT_TIMESTAMP,
                            amount_paid REAL DEFAULT 0,
                            services TEXT,
                            note TEXT,
                            FOREIGN KEY(patient_id) REFERENCES patients(id) ON DELETE CASCADE)''')

        self.conn.commit()
        cursor.close()

    def add_patient(self, name, age, phone):
        cursor = self.conn.cursor()
        cursor.execute("INSERT INTO patients (name, age, phone) VALUES (?, ?, ?)", (name, age, phone))
        self.conn.commit()
        cursor.close()

    def add_service(self, name, cost):
        cursor = self.conn.cursor()
        cursor.execute("INSERT INTO services (name, cost) VALUES (?, ?)", (name, cost))
        self.conn.commit()
        cursor.close()

    def add_visit(self, patient_id, amount_paid, service_ids=None, note=None):
        cursor = self.conn.cursor()
        date = datetime.now().strftime('%Y-%m-%d')
        services = ','.join(map(str, service_ids)) if service_ids else ''
        cursor.execute("INSERT INTO visits (patient_id, date, amount_paid, services, note) VALUES (?, ?, ?, ?, ?)", 
                       (patient_id, date, amount_paid, services, note))
        visit_id = cursor.lastrowid
        self.conn.commit()
        cursor.close()
        return visit_id

    def get_patient_profile(self, patient_id):
        conn = self.conn
        cursor = conn.cursor()

        cursor.execute("SELECT id, name, age, phone FROM patients WHERE id = ?", (patient_id,))
        patient = cursor.fetchone()

        if not patient:
            cursor.close()
            return json.dumps({"error": "Patient not found"}, ensure_ascii=False)

        patient_info = {
            "id": patient[0],
            "name": patient[1],
            "age": patient[2],
            "phone": patient[3]
        }

        cursor.execute("SELECT id, date, amount_paid, services, note FROM visits WHERE patient_id = ?", (patient_id,))
        visits = cursor.fetchall()
        cursor.close()

        visit_list = []
        for visit in visits:
            visit_id, date, amount_paid, service_ids, note = visit
            date = datetime.fromisoformat(date.split()[0]).strftime('%Y-%m-%d')
            service_details = []

            if service_ids:
                service_ids = service_ids.split(',')
                service_cursor = conn.cursor()
                for service_id in service_ids:
                    service_cursor.execute("SELECT id, name, cost FROM services WHERE id = ?", (service_id,))
                    service = service_cursor.fetchone()
                    if service:
                        service_details.append({
                            "id": service[0],
                            "name": service[1],
                            "cost": service[2]
                        })
                service_cursor.close()

            visit_list.append({
                "id": visit_id,
                "date": date,
                "amount_paid": amount_paid,
                "note": note,
                "services": service_details
            })

        return {"patient": patient_info, "visits": visit_list}

    def get_all_patients(self):
        cursor = self.conn.cursor()
        cursor.execute("""
        SELECT p.id, p.name, p.age, p.phone, 
               MAX(v.date) AS last_visit_date
        FROM patients p
        LEFT JOIN visits v ON p.id = v.patient_id
        GROUP BY p.id
        """)
        rows = cursor.fetchall()
        cursor.close()

        return [{"id": r[0], "name": r[1], "age": r[2], "phone": r[3], "last_visit_date": r[4] if r[4] else 'لا يوجد'}
                for r in rows]

    def get_all_services(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT id, name, cost FROM services")
        rows = cursor.fetchall()
        cursor.close()

        return [{"id": r[0], "name": r[1], "cost": r[2]} for r in rows]

    def get_all_visits(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM visits")
        visits = cursor.fetchall()
        cursor.close()
        return visits
    
    def get_service(self, service_id):
        cursor = self.conn.cursor()
        cursor.execute("SELECT id, name, cost FROM services WHERE id = ?", (service_id,))
        service = cursor.fetchone()
        cursor.close()
        if not service:
            return None
        return {"id": service[0], "name": service[1], "cost": service[2]}
    
    def update_patient(self, patient_id, name, age, phone):
        cursor = self.conn.cursor()
        cursor.execute("UPDATE patients SET name = ?, age = ?, phone = ? WHERE id = ?", 
                       (name, age, phone, patient_id))
        self.conn.commit()
        cursor.close()

    def update_service(self, service_id, name, cost):
        cursor = self.conn.cursor()
        cursor.execute("UPDATE services SET name = ?, cost = ? WHERE id = ?", (name, cost, service_id))
        self.conn.commit()
        cursor.close()

    def delete_service(self, service_id):
        cursor = self.conn.cursor()
        cursor.execute("DELETE FROM services WHERE id = ?", (service_id,))
        self.conn.commit()
        cursor.close()

    def delete_patient(self, patient_id):
        cursor = self.conn.cursor()
        cursor.execute("DELETE FROM patients WHERE id = ?", (patient_id,))
        cursor.execute("DELETE FROM visits WHERE patient_id = ?", (patient_id,))
        self.conn.commit()
        cursor.close()

    def calculate_profit(self, start_date=None, end_date=None):
        cursor = self.conn.cursor()
    
        # Use the current month's start and end dates by default if no dates are provided
        if not start_date or not end_date:
            today = datetime.now()
            start_date = today.replace(day=1).strftime('%Y-%m-%d')  # First day of the current month
            end_date = today.replace(day=28) + timedelta(days=4)  # Last day of the current month
            end_date = end_date.replace(day=1) - timedelta(days=1)
    
        # Calculate total revenue (amount_paid) for the specified date range
        cursor.execute("""
            SELECT SUM(amount_paid) 
            FROM visits 
            WHERE date BETWEEN ? AND ?
        """, (start_date, end_date))
        revenue = cursor.fetchone()[0] or 0
        # Calculate total cost for the specified date range
        cursor.execute("""
            SELECT services 
            FROM visits 
            WHERE date BETWEEN ? AND ?
        """, (start_date, end_date))
        visits = cursor.fetchall()
    
        total_cost = 0
        for visit in visits:
            service_ids = visit[0]
            if service_ids:  # If there are services linked to this visit
                service_ids = service_ids.split(',')  # Split the comma-separated service IDs
                for service_id in service_ids:
                    cursor.execute("SELECT cost FROM services WHERE id = ?", (service_id,))
                    service = cursor.fetchone()
                    if service:
                        total_cost += service[0]  # Add the service cost to the total cost
    
        cursor.close()
    
        return [revenue, total_cost]
    
    def get_patients_count(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM patients")
        count = cursor.fetchone()[0]
        cursor.close()
        return count
    
    def get_services_count(self):
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM services")
        count = cursor.fetchone()[0]
        cursor.close()
        return count
    
    def close(self):
        self.conn.close()
