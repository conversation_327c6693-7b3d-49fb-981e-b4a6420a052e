from flask import Flask, render_template, jsonify, request
from utils import DATABASE_manager
import os
import threading
import time
import subprocess
import signal
import shutil
import psutil

app = Flask(__name__)
db = DATABASE_manager()

def signal_handler(sig, frame):
    print('Exiting...')
    os._exit(0)

signal.signal(signal.SIGINT, signal_handler)

@app.route('/patients', methods=['GET'])
def get_all_patients():
    patients = db.get_all_patients()
    return jsonify(patients)


@app.route('/patient/<int:patient_id>', methods=['GET'])
def get_patient_profile(patient_id):
    profile = db.get_patient_profile(patient_id)
    if profile:
        return jsonify(profile)
    return jsonify({"error": "Patient not found"}), 404


@app.route('/patient', methods=['POST'])
def add_patient():
    data = request.get_json()
    name = data['name']
    age = data['age']
    phone = data['phone']
    try:
        db.add_patient(name, age, phone)
        return jsonify({"message": "Patient added successfully"}), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400


@app.route('/patient/<int:patient_id>', methods=['PUT'])
def update_patient(patient_id):
    data = request.get_json()
    name = data['name']
    age = data['age']
    phone = data['phone']
    db.update_patient(patient_id, name, age, phone)
    return jsonify({"message": "Patient updated successfully"})


@app.route('/patient/<int:patient_id>', methods=['DELETE'])
def delete_patient(patient_id):
    db.delete_patient(patient_id)
    return jsonify({"message": "Patient deleted successfully"})


@app.route('/services', methods=['GET'])
def get_all_services():
    services = db.get_all_services()
    return jsonify(services)


@app.route('/services', methods=['POST'])
def add_service():
    data = request.get_json()
    name = data['name']
    cost = data['cost']
    db.add_service(name, cost)
    return jsonify({"message": "Service added successfully"})


@app.route('/services/<int:service_id>', methods=['GET'])
def get_service(service_id):
    service = db.get_service(service_id)
    if service:
        return jsonify(service)
    return jsonify({"error": "Service not found"}), 404


@app.route('/services/<int:service_id>', methods=['PUT'])
def update_service(service_id):
    data = request.get_json()
    name = data['name']
    cost = data['cost']
    db.update_service(service_id, name, cost)
    return jsonify({"message": "Service updated successfully"})


@app.route('/services/<int:service_id>', methods=['DELETE'])
def delete_service(service_id):
    db.delete_service(service_id)
    return jsonify({"message": "Service deleted successfully"})


@app.route('/visit', methods=['POST'])
def add_visit():
    data = request.get_json()
    patient_id = data['patient_id']
    amount_paid = data['amount_paid']
    service_ids = data.get('service_ids', [])
    print(service_ids)
    note = data.get('note', '')
    visit_id = db.add_visit(patient_id, amount_paid, service_ids, note)
    return jsonify({"message": "Visit added successfully", "visit_id": visit_id})


@app.route('/profit', methods=['GET'])
def get_profit():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    profit = db.calculate_profit(start_date, end_date)
    total_profit = profit[0] - profit[1]
    return jsonify({
        'revenue': profit[0],
        'cost': profit[1],
        'profit': total_profit
    })


@app.route('/')
def dashboard():
    return render_template('index.html', patients_count=db.get_patients_count(),
                           services_count=db.get_services_count())

def is_thorium_running():
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == 'thorium.exe':
            return True
    return False
# Launch Thorium and monitor it
def launch_thorium():
    thorium_path = fr"C:\Users\<USER>\AppData\Local\Thorium\Application\thorium.exe"
    url = "http://127.0.0.1:5000/"
    flags = [
        "--start-maximized",
        "--app=" + url,
        "--disable-infobars",
        "--disable-session-crashed-bubble",
        "--disable-features=Translate,AutofillServerCommunication,InterestFeedContentSuggestions,AutomationControlled",
        "--disable-background-networking",
        "--disable-component-update",
        "--disable-sync",
        "--disable-extensions",
        "--disable-popup-blocking",
        "--disable-autofill",
        "--disable-password-generation",
        "--disable-save-password-bubble",
        "--disable-default-apps",
        "--noerrdialogs",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-breakpad",
        "--disable-client-side-phishing-detection",
        "--disable-dev-shm-usage",
        "--disable-features=site-per-process",
        "--disable-hang-monitor",
        "--disable-popup-blocking",
        "--disable-prompt-on-repost",
        "--disable-renderer-backgrounding",
        "--disable-translate",
        "--disable-web-resources",
        "--force-fieldtrials",
        "--ignore-ssl-errors",
        "--log-level=0",
        "--metrics-recording-only",
        "--mute-audio",
        "--no-default-browser-check",
        "--no-experiments",
        "--no-first-run",
        "--no-service-autorun",
        "--no-user-gesture-required",
        "--safebrowsing-disable-auto-update",
        "--use-mock-keychain",
        "--enable-quic",
        "wmic process where name='thorium.exe' CALL setpriority 128"
    ]
    temp_user_data_dir = os.path.join(os.getcwd(), "tmp_user_data")
    if not os.path.exists(temp_user_data_dir):os.makedirs(temp_user_data_dir)
    process = subprocess.Popen([thorium_path] + flags)
    while is_thorium_running():time.sleep(1)
    print("Thorium closed, shutting down Flask...")
    if os.path.exists(temp_user_data_dir):
        os.system(f"taskkill /f /im thorium.exe")
        shutil.rmtree(temp_user_data_dir)
    os._exit(0)


if __name__ == '__main__':
    flask_thread = threading.Thread(target=lambda: app.run(debug=False, use_reloader=False))
    flask_thread.start()
    launch_thorium()
    flask_thread.join()