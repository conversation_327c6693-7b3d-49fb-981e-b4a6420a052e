<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <link rel="icon" type="image/x-icon" href="/static/imgs/logo.png">
      <title>4U beauty clinic</title>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
      <link rel="stylesheet" href="static/style.css">
      <script defer src="static/script.js"></script>
   </head>
   <body>
      <button class="floating-theme-toggle" onclick="toggleTheme()">
         <i id="theme-icon" class="fas fa-moon"></i>
     </button>
      <nav class="sidebar">
         <div class="logo">4U beauty clinic</div>
         <ul>
            <li><a href="#home"><i class="fas fa-home"></i> الرئيسية</a></li>
            <li><a href="#patients"><i class="fas fa-users"></i> المراجعين</a></li>
            <li><a href="#services"><i class="fas fa-cogs"></i> الجلسات</a></li>
            <li><a href="#profit"><i class="fas fa-dollar-sign"></i> الربح</a></li>
         </ul>
      </nav>
      <div class="main-content">
         <header>
            <button class="menu-toggle"><i class="fas fa-bars"></i></button>
            <h1>4U beauty clinic</h1>
            <div class="logo">
               <img src="/static/imgs/logo.png" alt="4U Logo">
            </div>
         </header>
         <section id="home" class="active-section">
            <!-- <section class="quick-access">
               <h2>🚀 الوصول السريع</h2>
               <div class="toolbar">
                  <button id="patients-btn"><i class="fas fa-users"></i> المراجعين</button>
                  <button id="services-btn"><i class="fas fa-cogs"></i> الجلسات</button>
                  <button id="profit-btn"><i class="fas fa-dollar-sign"></i> الربح</button>
               </div>
            </section> -->
            <section class="dashboard-section">
               <div class="card">عدد المراجعين <span>{{ patients_count }}</span></div>
               <div class="card">عدد الجلسات <span>{{ services_count }}</span></div>
            </section>
         </section>
         <section id="patients" class="hidden-section">
            <h2>👨‍⚕️ المراجعين</h2>
            <div class="search-container">
               <i class="fas fa-search"></i>
               <input type="text" id="search-bar" placeholder="ابحث عن مراجع..." class="search-bar">
               <button id="add-patient-btn" onclick="openAddPatientModal()"><i class="fas fa-plus"></i> اضافة مراجع</button>
            </div>
            <div id="add-patient-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-add-patient-modal">&times;</span>
                  <h3>إضافة مراجع جديد</h3>
                  <div class="line"></div>
                  <form id="add-patient-form" autocomplete="off">
                     <button id="reset-form-add-patient">حذف</button>
                     <script>
                        document.getElementById("reset-form-add-patient").addEventListener("click", function() {
                           document.getElementById("add-name").value = "";
                           document.getElementById("add-age").value = "";
                           document.getElementById("add-phone").value = "";
                        });
                     </script>
                     <label for="add-name">الاسم:</label>
                     <input type="text" id="add-name" name="name" required>
                     <label for="add-age">العمر:</label>
                     <input type="number" id="add-age" name="age" required>
                     <label for="add-phone">رقم الجوال:</label>
                     <input type="text" id="add-phone" name="phone" required>
                     <button type="submit">إضافة</button>
                  </form>
               </div>
            </div>
            <!-- Example for the Delete Confirmation Modal -->
            <div id="delete-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-delete-modal">&times;</span>
                  <h3>هل أنت متأكد أنك تريد حذف هذا المراجع؟</h3>
                  <div class="line"></div>
                  <div style="display: flex; justify-content: center;gap: 10px;">
                     <button id="confirm-delete-btn">نعم</button>
                     <button id="cancel-delete-btn">إلغاء</button>
                  </div>
                  
               </div>
            </div>
            <!-- Example for the Edit Patient Modal -->
            <div id="edit-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-edit-modal">&times;</span>
                  <h3>تعديل المراجع</h3>
                  <div class="line"></div>
                  <form id="edit-form" autocomplete="off">
                     <button id="reset-form-edit">حذف</button>
                     <script>
                        document.getElementById("reset-form-edit").addEventListener("click", function() {
                           document.getElementById("edit-name").value = "";
                           document.getElementById("edit-age").value = "";
                           document.getElementById("edit-phone").value = "";
                        });
                     </script>
                     <label for="edit-name">الاسم:</label>
                     <input type="text" id="edit-name" name="name" disabled>
                     <label for="edit-phone">رقم الجوال:</label>
                     <input type="text" id="edit-phone" name="phone">
                     <label for="edit-age">العمر:</label>
                     <input type="number" id="edit-age" name="age" required>
                     <button type="submit">تعديل</button>
                  </form>
               </div>
            </div>
            <!-- Example for the Profile View Modal -->
            <div id="profile-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-profile-modal">&times;</span>
                  <h3>عرض الملف الشخصي</h3>
                  <div class="line"></div>
                  <div id="profile-details">
                     <p>الاسم: ${profile.patient.name}</p>
                     <p>العمر: ${profile.patient.age}</p>
                     <p>رقم الهاتف: ${profile.patient.phone}</p>
                     <table>
                        <thead>
                           <tr>
                              <th>تاريخ الزيارة</th>
                              <th>المبلغ المدفوع</th>
                              <th>الجلسات المقدمة</th>
                              <th>الملاحظات</th>
                           </tr>
                        </thead>
                        <tbody>
                           <tr>
                              <td>{ visit.date }</td>
                              <td>{ visit.amount_paid }</td>
                              <td>{ visit.services }</td>
                              <td>{ visit.notes }</td>
                           </tr>
                        </tbody>
                     </table>
                     <!-- Patient profile details will be displayed here dynamically -->
                  </div>
               </div>
            </div>
            <div id="add-visit-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-add-visit-modal">&times;</span>
                  <h3>إضافة زيارة للمراجع</h3>
                  <div class="line"></div>
                  <form id="add-visit-form" autocomplete="off">
                     <button id="reset-form-visit">حذف</button>
                     <script>
                        document.getElementById("reset-form-visit").addEventListener("click", function() {
                           //document.getElementById("visit-date").value = "";
                           document.getElementById("visit-date").value = new Date().toISOString().split('T')[0];
                           document.getElementById("amount-paid").value = "";
                           document.getElementById("note").value = "";
                           //document.getElementById("services-container").innerHTML = "";
                           document.querySelectorAll('#services-container input[type="checkbox"]').forEach(checkbox => checkbox.checked = false);
                           document.getElementById("services-error").style.display = "none";
                        });
                     </script>
                     <input type="hidden" id="patient-id" name="patient_id">
                     <label for="visit-date">تاريخ الزيارة:</label>
                     <input type="date" id="visit-date" name="date" required>
                     <label for="amount-paid">المبلغ المدفوع:</label>
                     <input type="number" id="amount-paid" name="amount_paid" required>
                     <label for="note">الملاحظات:</label>
                     <textarea id="note" name="note" class="note-input" style="resize: none;height: 100px"></textarea>
                     <label for="services">الجلسات المقدمة:</label>
                     <div id="services-container"></div>
                     <p id="services-error" style="color: red; display: none;">يجب اختيار جلسة واحدة على الأقل</p>
                     <button type="submit">إضافة الزيارة</button>
                  </form>
               </div>
            </div>
            <table class="patients-table">
               <thead>
                  <tr>
                     <th>اسم المراجع</th>
                     <th>رقم الجوال</th>
                     <th>تاريخ اخر زيارة</th>
                     <th>حذف</th>
                     <th>تعديل</th>
                     <th>عرض الملف الشخصي</th>
                     <th>إضافة زيارة</th>
                  </tr>
               </thead>
               <tbody id="patients-list">
                  <!-- Dynamic content will be loaded here using JavaScript -->
               </tbody>
            </table>
         </section>
         <section id="services" class="hidden-section">
            <h2>⚙ الجلسات</h2>
            <div class="search-container">
               <i class="fas fa-search"></i>
               <input type="text" id="search-barx" placeholder="ابحث عن جلسة..." class="search-bar">
               <button id="add-services-btn" onclick="openAddServiceModal()"><i class="fas fa-plus"></i> اضافة جلسة</button>
            </div>
            <div id="add-service-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-add-service-modal">&times;</span>
                  <h3>إضافة جلسة جديدة</h3>
                  <div class="line"></div>
                  <form id="add-service-form" autocomplete="off">
                     <button id="reset-form-service">حذف</button>
                     <script>
                        document.getElementById("reset-form-service").addEventListener("click", function() {
                           document.getElementById("add-namex").value = "";
                           document.getElementById("add-cost").value = "";
                        });
                     </script>
                     <label for="add-namex">الاسم:</label>
                     <input type="text" id="add-namex" name="name" required>


                     <label for="add-cost">التكلفة:</label>
                     <input type="number" id="add-cost" name="cost" required>

                     <button type="submit">إضافة</button>
                  </form>
               </div>
            </div>

            <div id="delete-modalx" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-delete-modalx">&times;</span>
                  <h3>هل أنت متأكد أنك تريد حذف هذه الجلسة</h3>
                  <div class="line"></div>
                  <div style="display: flex; justify-content: center;gap: 10px;">
                     <button id="confirm-delete-btnx">نعم</button>
                     <button id="cancel-delete-btnx">إلغاء</button>
                  </div>
               </div>
            </div>

            <div id="edit-service-modal" class="modal">
               <div class="modal-content">
                  <span class="close-btn" id="close-edit-modalx">&times;</span>
                  <h3>تعديل الجلسة</h3>
                  <div class="line"></div>
                  <form id="edit-service-form" autocomplete="off">
                     <button id="reset-form-editx">حذف</button>
                     <script>
                        document.getElementById("reset-form-editx").addEventListener("click", function() {
                           document.getElementById("edit-namex").value = "";
                           document.getElementById("edit-costx").value = "";
                        });
                     </script>
                     <label for="edit-namex">الاسم:</label>
                     <input type="text" id="edit-namex" name="name" disabled>
                     <label for="edit-costx">التكلفة:</label>
                     <input type="number" id="edit-costx" name="cost" required>
                     <button type="submit">تعديل</button>
                  </form>
               </div>
            </div>

            <table class="services-table">
               <thead>
                  <tr>
                     <th>اسم الجلسة</th>
                     <th>التكلفة</th>
                     <th>حذف</th>
                     <th>تعديل</th>
                  </tr>
               </thead>
               <tbody id="services-list">
                  <!-- Dynamic content will be loaded here using JavaScript -->
               </tbody>
            </table>
         </section>
         <section id="profit" class="hidden-section">
            <h2>📈 الربح</h2>
            <div class="date-filter">
                <label for="start-date">من:</label>
                <input type="date" id="start-date">
                <label for="end-date">إلى:</label>
                <input type="date" id="end-date">
                <button id="filter-profit">عرض الربح</button>
            </div>
        
            <!-- Cards to display the revenue, cost, and profit -->
            <div id="profit-cards-container">
                <div class="profit-card">
                    <h3>الإيرادات</h3>
                    <p id="revenue"></p>
                </div>
                <div class="profit-card">
                    <h3>التكاليف</h3>
                    <p id="cost"></p>
                </div>
                <div class="profit-card">
                    <h3>الربح</h3>
                    <p id="profit-v"></p>
                </div>
            </div>
        </section>
             
      </div>
   </body>
</html>