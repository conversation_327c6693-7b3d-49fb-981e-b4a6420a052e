document.addEventListener("DOMContentLoaded", function () {
    setTimeout(() => {
        let inputs = document.querySelectorAll("input");
        inputs.forEach(input => {
            input.setAttribute("autocomplete", "nope");
            input.setAttribute("autofill", "false");
            input.setAttribute("name", Array(10).fill(0).map(() => Math.random().toString(36).substring(2)).join('').substring(0, 200));
            input.setAttribute("readonly", "true");
            setTimeout(() => input.removeAttribute("readonly"), 500); // Re-enable after short delay
        });
    }, 1000);
});


// Toggle dark/light theme
function toggleTheme() {
    document.body.classList.toggle("dark-mode");
    const isDark = document.body.classList.contains("dark-mode");
    localStorage.setItem("theme", isDark ? "dark" : "light");
    document.getElementById("theme-icon").className = isDark ? "fas fa-sun" : "fas fa-moon";
}

// Set theme from localStorage or system preference
document.addEventListener("DOMContentLoaded", function () {
    const savedTheme = localStorage.getItem("theme") || (window.matchMedia('(prefers-color-scheme: dark)').matches ? "dark" : "light");
    if (savedTheme === "dark") {
        document.body.classList.add("dark-mode");
    }

    // Sidebar toggle functionality
    const sidebar = document.querySelector(".sidebar");
    const toggleButton = document.querySelector(".menu-toggle");
    const overlay = document.createElement("div");
    overlay.classList.add("overlay");
    document.body.appendChild(overlay);

    function toggleSidebar() {
        sidebar.classList.toggle("active");
        overlay.classList.toggle("active");
    }

    toggleButton.addEventListener("click", toggleSidebar);
    overlay.addEventListener("click", toggleSidebar);

    // Switch sections based on sidebar selection
    function switchSection(event) {
        event.preventDefault();
        const targetId = this.getAttribute("href").substring(1);
        const targetSection = document.getElementById(targetId);
        if (!targetSection) return;

        document.querySelectorAll("section").forEach((section) => {
            section.classList.toggle("hidden-section", section !== targetSection && !targetSection.contains(section));
            section.classList.toggle("active-section", section === targetSection);
        });

        document.querySelectorAll(".sidebar ul li a").forEach((link) => {
            link.classList.toggle("active", link === this);
        });

        if (sidebar.classList.contains("active")) {
            toggleSidebar();
        }
    }

    document.querySelectorAll(".sidebar ul li a").forEach((link) => {
        link.addEventListener("click", switchSection);
    });

    document.querySelector(".sidebar ul li a[href='#home']").classList.add("active");
});

// Search for patients
const searchInput = document.getElementById('search-bar');
searchInput.addEventListener('input', function () {
    const searchTerm = searchInput.value.toLowerCase();
    var patientRows = document.querySelectorAll('.patients-table tbody tr');
    patientRows.forEach(row => {
        const patientName = row.cells[0].textContent.toLowerCase();
        const phoneNumber = row.cells[1].textContent.toLowerCase();
        const lastVisitDate = row.cells[2].textContent.toLowerCase();
        row.style.display = (patientName.includes(searchTerm) || phoneNumber.includes(searchTerm) || lastVisitDate.includes(searchTerm)) ? '' : 'none';
    });
});

// Fetch all patients and update the table
function fetchPatients() {
    fetch('/patients')
        .then(response => response.json())
        .then(patients => {
            const patientsTable = document.getElementById('patients-list');
            patientsTable.innerHTML = ''; // Clear existing rows

            patients.forEach(patient => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${patient.name}</td>
                    <td>${patient.phone}</td>
                    <td>${patient.last_visit_date || "لا يوجد بيانات"}</td>
                    <td><button class="delete-btn" onclick="openDeleteModal(${patient.id})">حذف</button></td>
                    <td><button class="edit-btn" onclick="openEditModal(${patient.id})">تعديل</button></td>
                    <td><button class="profile-btn" onclick="viewProfile(${patient.id})">عرض الملف الشخصي</button></td>
                    <td><button class="add-visit-btn" onclick="openAddVisitModal(${patient.id})">إضافة زيارة</button></td>
                `;
                patientsTable.appendChild(row);
            });
        });
}

// Handle deleting a patient
function deletePatient(patientId) {
    fetch(`/patient/${patientId}`, { method: 'DELETE' })
        .then(response => response.json())
        .then(() => {
            closeDeleteModal();
            fetchPatients(); // Refresh the list
        });
}

// Open modal for deleting a patient
function openDeleteModal(patientId) {
    const deleteModal = document.getElementById('delete-modal');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    deleteModal.style.display = 'flex';
    
    confirmDeleteBtn.onclick = function() {
        deletePatient(patientId);
    };
}

// Close the delete modal
function closeDeleteModal() {
    document.getElementById('delete-modal').style.display = 'none';
}

// Open the edit patient modal
function openEditModal(patientId) {
    const editModal = document.getElementById('edit-modal');
    fetch(`/patient/${patientId}`)
        .then(response => response.json())
        .then(patient => {
            document.getElementById('edit-name').value = patient.patient.name;
            document.getElementById('edit-phone').value = patient.patient.phone;
            document.getElementById('edit-age').value = patient.patient.age;
        });

    editModal.style.display = 'flex';
    document.getElementById('edit-form').onsubmit = function(event) {
        event.preventDefault();
        const name = document.getElementById('edit-name').value;
        const phone = document.getElementById('edit-phone').value;
        const age = document.getElementById('edit-age').value;

        fetch(`/patient/${patientId}`, {
            method: 'PUT',
            body: JSON.stringify({ name, phone, age }),
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(() => {
            fetchPatients();
            closeEditModal();
        });
    };
}

// Close the edit modal
function closeEditModal() {
    document.getElementById('edit-modal').style.display = 'none';
}

// Open the profile modal
function viewProfile(patientId) {
    const profileModal = document.getElementById('profile-modal');
    fetch(`/patient/${patientId}`)
        .then(response => response.json())
        .then(profile => {
            const profileDetails = document.getElementById('profile-details');
            let visitsHTML = '';

            profile.visits.forEach(visit => {
                let serviceNames = visit.services.map(service => service.name).join(', ');
                visitsHTML += `
                    <tr>
                        <td>${visit.date}</td>
                        <td>${visit.amount_paid}</td>
                        <td style="text-align: center;width:250px">${serviceNames}</td>
                        <td><textarea class="note-content" readonly>${visit.note || 'لا توجد ملاحظات'}</textarea></td>
                    </tr>
                `;
            });

            profileDetails.innerHTML = `
                <p>الاسم: ${profile.patient.name}</p>
                <p>العمر: ${profile.patient.age}</p>
                <p>رقم الهاتف: ${profile.patient.phone}</p>
                <table>
                    <thead>
                        <tr>
                            <th>تاريخ الزيارة</th>
                            <th>المبلغ المدفوع</th>
                            <th>الجلسات المقدمة</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${visitsHTML}
                    </tbody>
                </table>
            `;
        });

    profileModal.style.display = 'flex';
}

// Handle adding a patient
document.getElementById('add-patient-form').onsubmit = function(event) {
    event.preventDefault();
    const name = document.getElementById('add-name').value;
    const age = document.getElementById('add-age').value;
    const phone = document.getElementById('add-phone').value;
    fetch('/patient', {
        method: 'POST',
        body: JSON.stringify({ name, age, phone }),
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(() => {
        closeAddPatientModal();
        fetchPatients(); // Refresh the list
    });
}

// Open the add patient modal
function openAddPatientModal() {
    document.getElementById('add-patient-modal').style.display = 'flex';
}

// Close the add patient modal
function closeAddPatientModal() {
    document.getElementById('add-patient-modal').style.display = 'none';
}

function closeAddVisitModal() {
    document.getElementById('add-visit-modal').style.display = 'none';
}

// Add a visit to the patient
function openAddVisitModal(patientId) {
    const addVisitModal = document.getElementById('add-visit-modal');
    document.getElementById('patient-id').value = patientId;
    fetchPatients();
    addVisitModal.style.display = 'flex';
}

// Handle adding a visit
document.getElementById('add-visit-form').onsubmit = function(event) {
    event.preventDefault();

    const visitDate = document.getElementById('visit-date').value;
    const patientId = document.getElementById('patient-id').value;
    const amountPaid = document.getElementById('amount-paid').value;
    const note = document.getElementById('note').value;
    const service_ids = Array.from(document.querySelectorAll('#services-container input[type="checkbox"]:checked'))
                          .map(checkbox => checkbox.value);

    const errorMsg = document.getElementById('services-error');
    if (services.length === 0) {
        errorMsg.style.display = 'block'; // Show error message
        return; // Stop form submission
    } else {
        errorMsg.style.display = 'none'; // Hide error if selection is valid
    }
    fetch('/visit', {
        method: 'POST',
        body: JSON.stringify({ patient_id: patientId, date: visitDate, amount_paid: amountPaid, service_ids, note }),
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(() => {
        closeAddVisitModal();
        fetchPatients(); // Refresh the list
    });
}

// Event listeners for closing modals
document.getElementById('close-delete-modal').addEventListener('click', closeDeleteModal);
document.getElementById('close-edit-modal').addEventListener('click', closeEditModal);
document.getElementById('close-add-visit-modal').addEventListener('click', closeAddVisitModal);
document.getElementById('close-add-patient-modal').addEventListener('click', closeAddPatientModal);
document.getElementById('close-profile-modal').addEventListener('click', function () {
    document.getElementById('profile-modal').style.display = 'none';
});

// Close modals if clicked outside the modal content
window.addEventListener('click', function (event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
});

// Fetch patients on page load
document.addEventListener('DOMContentLoaded', fetchPatients);
document.addEventListener('DOMContentLoaded', fetchServices);

document.addEventListener("DOMContentLoaded", function () {
    const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
    document.getElementById('visit-date').value = today;
});

function openAddServiceModal() {
    document.getElementById('add-service-modal').style.display = 'flex';
}

function closeAddServiceModal() {
    document.getElementById('add-service-modal').style.display = 'none';
}
document.getElementById('close-add-service-modal').addEventListener('click', closeAddServiceModal);

document.getElementById('add-service-form').onsubmit = function(event) {
    event.preventDefault();
    const name = document.getElementById("add-namex").value.trim();
    const cost = document.getElementById("add-cost").value.trim();
    
    if (!name || !cost) {
        alert("يجب ملء جميع الحقول!");
        return;
    }
    fetch('/services', {
        method: 'POST',
        body: JSON.stringify({ name, cost }),
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(() => {
        closeAddServiceModal();
        fetchServices();
    });
}

function fetchServices() {
    fetch('/services')
        .then(response => response.json())
        .then(services => {
            const servicesTable = document.getElementById('services-list');
            servicesTable.innerHTML = ''; // Clear existing rows
            const servicesContainer = document.getElementById('services-container'); // The container for checkboxes
            servicesContainer.innerHTML = ''; // Clear existing checkboxes
            services.forEach(service => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${service.name}</td>
                    <td>${service.cost}</td>
                    <td><button class="delete-btn" onclick="openDeleteModalService(${service.id})">حذف</button></td>
                    <td><button class="edit-btn" onclick="openEditModalService(${service.id})">تعديل</button></td>
                `;
                servicesTable.appendChild(row);
            });
            services.forEach(service => {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = 'services';
                checkbox.value = service.id;
                checkbox.id = `service-${service.id}`;

                const label = document.createElement('label');
                label.htmlFor = `service-${service.id}`;
                label.textContent = service.name;

                const div = document.createElement('div');
                div.classList.add('service-item'); // Optional: Add a class for styling
                div.appendChild(checkbox);
                div.appendChild(label);

                servicesContainer.appendChild(div);
            });
        });
}

function deleteService(serviceId) {
    fetch(`/services/${serviceId}`, { method: 'DELETE' })
        .then(response => response.json())
        .then(() => {
            fetchServices(); // Refresh the list
            closeDeleteModalService();
        });
}

function openDeleteModalService(serviceId) {
    const deleteModal = document.getElementById('delete-modalx');
    const confirmDeleteBtn = document.getElementById('confirm-delete-btnx');
    deleteModal.style.display = 'flex';
    
    confirmDeleteBtn.onclick = function() {
        deleteService(serviceId);
    };
}
function closeDeleteModalService() {
    document.getElementById('delete-modalx').style.display = 'none';
}

function openEditModalService(serviceId) {
    const editModal = document.getElementById('edit-service-modal');
    fetch(`/services/${serviceId}`)
        .then(response => response.json())
        .then(service => {
            document.getElementById('edit-namex').value = service.name;
            document.getElementById('edit-costx').value = service.cost;
        });
    editModal.style.display = 'flex';
    document.getElementById('edit-service-form').onsubmit = function(event) {
        event.preventDefault();
        const name = document.getElementById('edit-namex').value;
        const cost = document.getElementById('edit-costx').value;
        fetch(`/services/${serviceId}`, {
            method: 'PUT',
            body: JSON.stringify({ name, cost }),
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(() => {
            fetchServices(); // Refresh the list
            closeEditModalService();
        });
    };
}

function closeEditModalService() {
    document.getElementById('edit-service-modal').style.display = 'none';
}

document.getElementById('close-delete-modalx').addEventListener('click', closeDeleteModalService);
document.getElementById('cancel-delete-btn').addEventListener('click', closeDeleteModal);
document.getElementById('cancel-delete-btnx').addEventListener('click', closeDeleteModalService);
document.getElementById('close-edit-modalx').addEventListener('click', closeEditModalService);

const searchInputx = document.getElementById('search-barx');

searchInputx.addEventListener('input', function () {
    const searchTerm = searchInputx.value.trim().toLowerCase();
    var serviceRows = document.querySelectorAll('.services-table tbody tr');
    serviceRows.forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const cost = row.cells[1].textContent.toLowerCase();
        if (name.includes(searchTerm) || cost.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});





document.getElementById('filter-profit').addEventListener('click', function() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    const url = `/profit?start_date=${startDate}&end_date=${endDate}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            document.getElementById('revenue').textContent = `د.ع ${data.revenue.toFixed(2)}`;
            document.getElementById('cost').textContent = `د.ع ${data.cost.toFixed(2)}`;
            document.getElementById('profit-v').textContent = `د.ع ${data.profit.toFixed(2)}`;
        })
        .catch(error => {
            console.error('Error fetching profit data:', error);
            alert('فشل في تحميل البيانات.');
        });
});




