function toggleTheme() {
    document.body.classList.toggle("dark-mode");
    const isDark = document.body.classList.contains("dark-mode");
    localStorage.setItem("theme", isDark ? "dark" : "light");
    document.getElementById("theme-icon").className = isDark ? "fas fa-sun" : "fas fa-moon";
}

document.addEventListener("DOMContentLoaded", function () {
    const sidebar = document.querySelector(".sidebar");
    const toggleButton = document.querySelector(".menu-toggle");
    const overlay = document.createElement("div");
    overlay.classList.add("overlay");
    document.body.appendChild(overlay);

    function toggleSidebar() {
        sidebar.classList.toggle("active");
        overlay.classList.toggle("active");
    }

    toggleButton.addEventListener("click", toggleSidebar);
    overlay.addEventListener("click", toggleSidebar);

    function switchSection(event) {
        event.preventDefault();
        const targetId = this.getAttribute("href").substring(1);
        const targetSection = document.getElementById(targetId);
        if (!targetSection) return;

        document.querySelectorAll("section").forEach((section) => {
            if (section !== targetSection && !targetSection.contains(section)) {
                section.classList.add("hidden-section");
                section.classList.remove("active-section");
            } else {
                section.classList.remove("hidden-section");
                section.classList.remove("active-section");
            }
        });
        document.querySelectorAll(".sidebar ul li a").forEach((link) => {
            link.classList.remove("active");
        });
        this.classList.add("active");

        if (sidebar.classList.contains("active")) {
            toggleSidebar();
        }

    }

    document.querySelectorAll(".sidebar ul li a").forEach((link) => {
        link.addEventListener("click", switchSection);
    });

    document.querySelector(".sidebar ul li a[href='#home']").classList.add("active");
});

document.addEventListener("DOMContentLoaded", () => {
    const savedTheme = localStorage.getItem("theme") || (window.matchMedia('(prefers-color-scheme: dark)').matches ? "dark" : "light");
    if (savedTheme === "dark") {
        document.body.classList.add("dark-mode");
        document.getElementById("theme-icon").className = "fas fa-sun";
    }
});




// Get modal elements
const deleteModal = document.getElementById('delete-modal');
const editModal = document.getElementById('edit-modal');
const profileModal = document.getElementById('profile-modal');

// Get buttons
const deleteBtns = document.querySelectorAll('.delete-btn');
const editBtns = document.querySelectorAll('.edit-btn');
const profileBtns = document.querySelectorAll('.profile-btn');

// Close buttons
const closeDeleteModalBtn = document.getElementById('close-delete-modal');
const closeEditModalBtn = document.getElementById('close-edit-modal');
const closeProfileModalBtn = document.getElementById('close-profile-modal');

// Open and close modals
let rowToDelete = null; // To store row for deletion

// Open delete modal
deleteBtns.forEach(button => {
    button.addEventListener('click', function () {
        rowToDelete = this.closest('tr'); // Store the row to be deleted
        deleteModal.style.display = 'flex'; // Show the modal
    });
});

// Open edit modal and populate fields
editBtns.forEach(button => {
    button.addEventListener('click', function () {
        const row = this.closest('tr');
        const name = row.cells[0].textContent;
        const phone = row.cells[1].textContent;
        const visitDate = row.cells[2].textContent;

        document.getElementById('edit-name').value = name;
        document.getElementById('edit-phone').value = phone;
        document.getElementById('edit-visit').value = visitDate;

        editModal.style.display = 'flex'; // Show the modal
    });
});

// Open profile modal and show profile details
profileBtns.forEach(button => {
    button.addEventListener('click', function () {
        const row = this.closest('tr');
        const name = row.cells[0].textContent;
        const phone = row.cells[1].textContent;
        const visitDate = row.cells[2].textContent;

        document.getElementById('profile-details').innerHTML = `
            <p><strong>الاسم:</strong> ${name}</p>
            <p><strong>رقم الجوال:</strong> ${phone}</p>
            <p><strong>تاريخ آخر زيارة:</strong> ${visitDate}</p>
        `;
        
        profileModal.style.display = 'flex'; // Show the modal
    });
});

// Confirm delete action
const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
confirmDeleteBtn.addEventListener('click', function () {
    if (rowToDelete) {
        rowToDelete.remove(); // Remove the patient row
    }
    deleteModal.style.display = 'none'; // Close the delete modal
});

// Cancel delete action
const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
cancelDeleteBtn.addEventListener('click', function () {
    deleteModal.style.display = 'none'; // Close the delete modal
});

// Close modals when clicking on the close button (X)
closeDeleteModalBtn.addEventListener('click', function () {
    deleteModal.style.display = 'none';
});

closeEditModalBtn.addEventListener('click', function () {
    editModal.style.display = 'none';
});

closeProfileModalBtn.addEventListener('click', function () {
    profileModal.style.display = 'none';
});

// Close modals when clicking outside the modal content
window.addEventListener('click', function (event) {
    if (event.target === deleteModal) {
        deleteModal.style.display = 'none';
    }
    if (event.target === editModal) {
        editModal.style.display = 'none';
    }
    if (event.target === profileModal) {
        profileModal.style.display = 'none';
    }
});
