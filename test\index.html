<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <script defer src="script.js"></script>
</head>
<body>
    <nav class="sidebar">
        <div class="logo">دروب العراق</div>
        <ul>
            <li><a href="#home"><i class="fas fa-home"></i> الرئيسية</a></li>
            <li><a href="#patients"><i class="fas fa-users"></i> المراجعين</a></li>
            <li><a href="#services"><i class="fas fa-cogs"></i> الخدمات</a></li>
            <li><a href="#profit"><i class="fas fa-dollar-sign"></i> الربح</a></li>
        </ul>
    </nav>
    <div class="main-content">
        <header>
            <button class="menu-toggle"><i class="fas fa-bars"></i></button>
            <h1>4U Dashboard</h1>
            <div class="logo">
                <img src="imgs/logo.png" alt="4U Logo">
            </div>
        </header>

        <section id="home" class="hidden-section">
            <section class="quick-access">
                <h2>🚀 الوصول السريع</h2>
                <div class="toolbar">
                    <button id="patients-btn"><i class="fas fa-users"></i> المراجعين</button>
                    <button id="services-btn"><i class="fas fa-cogs"></i> الخدمات</button>
                    <button id="profit-btn"><i class="fas fa-dollar-sign"></i> الربح</button>
                </div>
            </section>

            <section class="dashboard-section">
                <div class="card">عدد المراجعين <span>{{ students_count }}</span></div>
                <div class="card">عدد الخدمات <span>{{ malazeem_count }}</span></div>
            </section>
        </section>

        <section id="patients" class="active-section">
            <h2>👨‍⚕️ المراجعين</h2>
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" id="search-bar" placeholder="ابحث عن مراجع..." class="search-bar">
            </div>
            <!-- Delete Confirmation Modal -->
<div id="delete-modal" class="modal">
    <div class="modal-content">
        <span class="close-btn" id="close-delete-modal">&times;</span>
        <h3>هل أنت متأكد أنك تريد حذف هذا المراجع؟</h3>
        <button id="confirm-delete-btn">نعم</button>
        <button id="cancel-delete-btn">إلغاء</button>
    </div>
</div>

<!-- Edit Patient Modal -->
<div id="edit-modal" class="modal">
    <div class="modal-content">
        <span class="close-btn" id="close-edit-modal">&times;</span>
        <h3>تعديل المراجع</h3>
        <form id="edit-form">
            <label for="edit-name">الاسم:</label>
            <input type="text" id="edit-name" name="name">
            <label for="edit-phone">رقم الجوال:</label>
            <input type="text" id="edit-phone" name="phone">
            <label for="edit-visit">تاريخ آخر زيارة:</label>
            <input type="date" id="edit-visit" name="visit">
            <button type="submit">تعديل</button>
        </form>
    </div>
</div>

<!-- View Profile Modal -->
<div id="profile-modal" class="modal">
    <div class="modal-content">
        <span class="close-btn" id="close-profile-modal">&times;</span>
        <h3>عرض الملف الشخصي</h3>
        <div id="profile-details">
            <!-- Patient profile details will be displayed here dynamically -->
        </div>
    </div>
</div>

             <table class="patients-table">
                <thead>
                    <tr>
                        <th>اسم المراجع</th>
                        <th>رقم الجوال</th>
                        <th>تاريخ اخر زيارة</th>
                        <th>حذف</th>
                        <th>تعديل</th>
                        <th>عرض الملف الشخصي</th>
                    </tr>
                </thead>
                <!-- <tbody>
                    {% for patient in patients %}
                        <tr>
                            <td>{{ patient.name }}</td>
                            <td>{{ patient.phone_number }}</td>
                            <td>{{ patient.last_visit_date }}</td>
                            <td><button class="delete-btn">حذف</button></td>
                            <td><button class="edit-btn">تعديل</button></td>
                            <td><button class="profile-btn">عرض الملف الشخصي</button></td>
                        </tr>
                    {% endfor %}
                </tbody> -->
                <tbody>
                    <tr>
                        <td>أحمد علي</td>
                        <td>01123456789</td>
                        <td>2025-01-20</td>
                        <td><button class="delete-btn">حذف</button></td>
                        <td><button class="edit-btn">تعديل</button></td>
                        <td><button class="profile-btn">عرض الملف الشخصي</button></td>
                    </tr>
                    <tr>
                        <td>مريم محمد</td>
                        <td>01234567890</td>
                        <td>2024-12-15</td>
                        <td><button class="delete-btn">حذف</button></td>
                        <td><button class="edit-btn">تعديل</button></td>
                        <td><button class="profile-btn">عرض الملف الشخصي</button></td>
                    </tr>
                    <tr>
                        <td>سامي حسين</td>
                        <td>01098765432</td>
                        <td>2025-02-01</td>
                        <td><button class="delete-btn">حذف</button></td>
                        <td><button class="edit-btn">تعديل</button></td>
                        <td><button class="profile-btn">عرض الملف الشخصي</button></td>
                    </tr>
                    <tr>
                        <td>ليلى أحمد</td>
                        <td>01122334455</td>
                        <td>2025-01-10</td>
                        <td><button class="delete-btn">حذف</button></td>
                        <td><button class="edit-btn">تعديل</button></td>
                        <td><button class="profile-btn">عرض الملف الشخصي</button></td>
                    </tr>
                </tbody>
            </table>
        </section>
    </div> 
</body>
</html>
