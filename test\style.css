:root {
    --primary-color: #1d4ed8;
    --secondary-color: #003eb3;
    --background-color: #f4f6f9;
    --text-color: #333;
    --white: #fff;
    --danger-color: #e63946;
    --edit-color: #4CAF50;
    --profile-color: #388E3C;
    --shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    --radius: 12px;
    --transition: all 0.3s ease-in-out;

    --dark-background-color: #1a1a1a;
    --dark-text-color: #fff;
    --dark-card-background: #2a2a2a;
    --dark-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--background-color);
    margin: 0;
    padding: 0;
    color: var(--text-color);
    line-height: 1.6;
}

.main-content {
    padding: 20px;
    width: calc(100% - 40px);
    max-width: 1200px;
    margin: 0 auto;
    transition: var(--transition);
}

header {
    display: flex;
    overflow: hidden;
    justify-content: space-between;
    align-items: center;
    background-color: var(--white);
    padding: 15px 20px;
    border-radius: var(--radius);
    position: relative;
    box-shadow: 0 4px 15px rgba(29, 78, 216, 0.2);
    gap: 20px;
}

header img {
    max-width: 100%;
    height: 50px;
}

header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1d4ed8, #003eb3);
    opacity: 0.8;
    transition: opacity 0.3s ease-in-out;
}

header:hover::before {
    opacity: 1;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--primary-color);
    transition: var(--transition);
}

.menu-toggle:hover {
    transform: scale(1.1);
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: var(--primary-color);
    color: var(--white);
    height: 100vh;
    padding: 20px;
    position: fixed;
    left: -300px;
    transition: var(--transition);
    box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.sidebar.active {
    left: 0;
    animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
    0% {
        left: -260px;
    }
    100% {
        left: 0;
    }
}

.sidebar .logo {
    font-size: 20px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 20px;
    position: relative;
}

.sidebar .logo::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    border-radius: 2px;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar ul li {
    margin: 10px 0;
}

.sidebar ul li a {
    color: var(--white);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: var(--radius);
    transition: background var(--transition);
}

.sidebar ul li a.active {
    background: var(--secondary-color);
}

.sidebar ul li a:hover {
    background: var(--secondary-color);
}

/* Overlay Effect */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0; /* Start hidden */
    visibility: hidden; /* Start hidden */
    transition: opacity 0.5s ease, visibility 0s 0.5s, backdrop-filter 0.5s ease; /* Transition for opacity, visibility, and backdrop-filter */
}

.overlay.active {
    opacity: 1; /* Fade in */
    visibility: visible; /* Make visible */
    backdrop-filter: blur(5px); /* Apply blur effect */
    transition: opacity 0.5s ease, visibility 0s 0s, backdrop-filter 0.5s ease; /* Transition for blur and opacity */
}

.quick-access {
    background: var(--white);
    padding: 15px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    margin-top: 20px;
}

.toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.toolbar button {
    flex: 1 1 150px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 10px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.toolbar button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.toolbar button:hover {
    background: var(--secondary-color);
    box-shadow: 0 0 15px rgba(29, 78, 216, 0.5);
}

.toolbar button:hover::before {
    opacity: 1;
}

.dashboard-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10%;
    grid-auto-flow: column;
    margin-top: 20px;
}

.card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--radius);
    box-shadow: 0 4px 15px rgba(29, 78, 216, 0.2);
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(29, 78, 216, 0.1), transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(29, 78, 216, 0.3);
}

.card:hover::before {
    opacity: 1;
}

.card span {
    display: block;
    font-size: 22px;
    font-weight: bold;
    color: var(--primary-color);
    margin-top: 10px;
}

.patients-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px, 0;
    text-align: center;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    background-color: var(--white);
    transition: var(--transition);
}

.patients-table thead tr {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

.patients-table th,
.patients-table td {
    padding: 12px 25px;
    text-align: center;
}

.patients-table tbody tr {
    border-bottom: 1px solid #444;
}

.patients-table tbody tr:nth-child(even) {
    background-color: rgba(29, 78, 216, 0.1);
}

.patients-table .delete-btn {
    flex: 1 1 150px;
    background: var(--danger-color);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.patients-table .delete-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.3), transparent 70%); /* Red radial gradient effect */
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.patients-table .delete-btn:hover {
    background: #d92d2f;
    box-shadow: 0 0 15px rgba(217, 45, 47, 0.6);
}

.patients-table .delete-btn:hover::before {
    opacity: 1;
}

.patients-table .edit-btn {
    flex: 1 1 150px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.patients-table .edit-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(29, 78, 216, 0.3), transparent 70%); /* Primary color radial gradient effect */
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.patients-table .edit-btn:hover {
    background: var(--secondary-color); /* Darker blue for the hover effect */
    box-shadow: 0 0 15px rgba(0, 63, 179, 0.5); /* Subtle blue glow shadow */
}

.patients-table .edit-btn:hover::before {
    opacity: 1;
}

.patients-table .profile-btn {
    flex: 1 1 150px;
    background: var(--profile-color); 
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.patients-table .profile-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(76, 175, 80, 0.3), transparent 70%); /* Light green radial gradient effect */
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.patients-table .profile-btn:hover {
    background: #388E3C;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.patients-table .profile-btn:hover::before {
    opacity: 1;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.search-bar {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--radius);
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
    color: var(--text-color);
}

.search-bar:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 8px rgba(29, 78, 216, 0.3);
}

.search-bar:hover {
    transform: translateY(-2px);
}


.modal {
    display: none; 
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
    z-index: 2;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: var(--radius);
    max-width: 500px;
    width: 100%;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 25px;
    cursor: pointer;
}

.modal button {
    padding: 10px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background 0.3s;
}

.modal button:hover {
    background: var(--secondary-color);
}

.modal button#cancel-delete-btn {
    background: #ccc;
    margin-right: 10px;
}

.modal button#confirm-delete-btn {
    background: #e74c3c;
}

.modal form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modal form label {
    font-weight: bold;
    color: var(--primary-color);
}

.modal form input[type="text"],
.modal form input[type="date"] {
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: var(--radius);
    background-color: var(--white);
    color: var(--text-color);
    transition: border-color 0.3s;
}

/* Focus effect on inputs */
.modal form input[type="text"]:focus,
.modal form input[type="date"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 8px rgba(29, 78, 216, 0.3);
}

/* Buttons */
.modal form button {
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background 0.3s;
    font-weight: bold;
}

/* Hover effect for buttons */
.modal form button:hover {
    background: var(--secondary-color);
}

#profile-details p {
    margin: 10px 0;
    font-size: 16px;
}

#profile-details p strong {
    color: var(--primary-color);
}


body.dark-mode {
    background-color: var(--dark-background-color);
    color: var(--dark-text-color);
}

body.dark-mode .main-content {
    background-color: var(--dark-background-color);
}

body.dark-mode header {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .menu-toggle {
    color: var(--primary-color);
}

body.dark-mode .sidebar {
    background: var(--dark-card-background);
}

body.dark-mode .toolbar button {
    background: var(--primary-color);
    color: var(--white);
}

body.dark-mode .toolbar button:hover {
    background: var(--secondary-color);
}

body.dark-mode .quick-access {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
    box-shadow: var(--dark-shadow);
}

body.dark-mode .card {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
    box-shadow: var(--dark-shadow);
}

body.dark-mode .card span {
    color: var(--primary-color);
}

body.dark-mode table {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
    box-shadow: var(--dark-shadow);
}

body.dark-mode table th,
body.dark-mode table td {
    border-color: #444;
}

body.dark-mode .search-bar {
    background: var(--dark-card-background);
    border-color: #444;
    color: var(--dark-text-color);
}

body.dark-mode .search-bar:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(29, 78, 216, 0.5);
    transition: var(--transition);
}

body.dark-mode .modal-content {
    background-color: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .modal button {
    background: var(--primary-color);
    color: var(--white);
}

body .dark-mode .modal button:hover {
    background: var(--secondary-color);
}

body.dark-mode .modal form input[type="text"],
body.dark-mode .modal form input[type="date"] {
    border-color: #444;
    background-color: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .modal form input[type="text"]:focus,
body.dark-mode .modal form input[type="date"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(29, 78, 216, 0.5);
}

body.dark-mode .modal form button {
    background: var(--primary-color);
    color: var(--white);
}

body.dark-mode .modal form button:hover {
    background: var(--secondary-color);
}


.hidden-section {
    display: none;
}

.active-section {
    display: block;
}