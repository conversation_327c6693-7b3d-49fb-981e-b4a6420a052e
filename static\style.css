* {font-size: large;}
:root {
    --primary-color: #d63384; /* Pink primary color */
    --secondary-color: #e83e8c; /* Lighter pink secondary color */
    --background-color: #f4f6f9;
    --text-color: #333;
    --white: #fff;
    --danger-color: #e63946;
    --edit-color: #4CAF50;
    --profile-color: #388E3C;
    --shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    --radius: 12px;
    --transition: all 0.3s ease-in-out;

    --dark-background-color: #1a1a1a;
    --dark-text-color: #fff;
    --dark-card-background: #2a2a2a;
    --dark-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--background-color);
    margin: 0;
    padding: 0;
    color: var(--text-color);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-smooth: always;
    font-size: large;
}

.main-content {
    padding: 20px;
    width: calc(100% - 40px);
    max-width: 1200px;
    margin: 0 auto;
    transition: var(--transition);
}

header {
    display: flex;
    overflow: hidden;
    justify-content: space-between;
    align-items: center;
    background-color: var(--white);
    padding: 15px 20px;
    border-radius: var(--radius);
    position: relative;
    /* box-shadow: 0 4px 15px rgba(29, 78, 216, 0.2); */
    box-shadow: 0 4px 15px rgba(231, 62, 140, 0.2);
    gap: 20px;
}

header img {
    display: block;
    margin: 0;
    max-width: 100%;
    max-height: 50px;
    height: auto;
}

header h1 {
    margin: 0;
    /* font-size: 24px; */
    font-size: large;
    font-weight: bold;
    color: var(--primary-color);
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #d63384, #e83e8c);
    opacity: 0.8;
    transition: opacity 0.3s ease-in-out;
}

header:hover::before {
    opacity: 1;
}

::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 5px;
    transition: background 0.3s ease-in-out;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

.menu-toggle {
    background: none;
    border: none;
    /* font-size: 24px; */
    font-size: large;
    cursor: pointer;
    color: var(--primary-color);
    transition: var(--transition);
}

.menu-toggle:hover {
    transform: scale(1.1);
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: var(--primary-color);
    color: var(--white);
    height: 100vh;
    padding: 20px;
    position: fixed;
    left: -300px;
    transition: var(--transition);
    box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.sidebar.active {
    left: 0;
    animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
    0% {
        left: -260px;
    }
    100% {
        left: 0;
    }
}

.sidebar .logo {
    /* font-size: 20px; */
    font-size: large;
    text-align: center;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 20px;
    position: relative;
}

.sidebar .logo::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    border-radius: 2px;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar ul li {
    margin: 10px 0;
}

.sidebar ul li a {
    color: var(--white);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: var(--radius);
    transition: background var(--transition);
}

.sidebar ul li a.active {
    background: var(--secondary-color);
}

.sidebar ul li a:hover {
    background: var(--secondary-color);
}

/* Overlay Effect */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0; /* Start hidden */
    visibility: hidden; /* Start hidden */
    transition: opacity 0.5s ease, visibility 0s 0.5s, backdrop-filter 0.5s ease; /* Transition for opacity, visibility, and backdrop-filter */
}

.overlay.active {
    opacity: 1; /* Fade in */
    visibility: visible; /* Make visible */
    backdrop-filter: blur(5px); /* Apply blur effect */
    transition: opacity 0.5s ease, visibility 0s 0s, backdrop-filter 0.5s ease; /* Transition for blur and opacity */
}

.quick-access {
    background: var(--white);
    padding: 15px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    margin-top: 20px;
}

.toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.toolbar button {
    flex: 1 1 150px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 10px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.toolbar button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.toolbar button:hover {
    background: var(--secondary-color);
    /* box-shadow: 0 0 15px rgba(29, 78, 216, 0.5); */
    box-shadow: 0 0 15px rgba(231, 62, 140, 0.5);
}

.toolbar button:hover::before {
    opacity: 1;
}

.dashboard-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10%;
    grid-auto-flow: column;
    margin-top: 20px;
}

.card {
    background: var(--white);
    padding: 25px;
    border-radius: var(--radius);
    /* box-shadow: 0 4px 15px rgba(29, 78, 216, 0.2); */
    box-shadow: 0 4px 15px rgba(231, 62, 140, 0.2);
    text-align: center;
    /* font-size: 18px; */
    font-size: large;
    font-weight: bold;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    /* background: radial-gradient(circle, rgba(29, 78, 216, 0.1), transparent 70%); */
    background: radial-gradient(circle, rgba(231, 62, 140, 0.1), transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.card:hover {
    transform: translateY(-5px);
    /* box-shadow: 0 8px 25px rgba(29, 78, 216, 0.3); */
    box-shadow: 0 8px 25px rgba(231, 62, 140, 0.3); 
}

.card:hover::before {
    opacity: 1;
}

.card span {
    display: block;
    /* font-size: 22px; */
    font-size: large;
    font-weight: bold;
    color: var(--primary-color);
    margin-top: 10px;
}

.patients-table,
#profile-details table,
.services-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px, 0;
    text-align: center;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    background-color: var(--white);
    transition: var(--transition);
}

.patients-table thead tr,
#profile-details thead tr,
.services-table thead tr {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

.patients-table th,
.patients-table td,
#profile-details th,
#profile-details td,
.services-table th,
.services-table td {
    padding: 12px 25px;
    text-align: center;
}

.patients-table tbody tr,
#profile-details tbody tr,
.services-table tbody tr {
    border-bottom: 1px solid #444;
}

.patients-table tbody tr:nth-child(even),
#profile-details tbody tr:nth-child(even),
.services-table tbody tr:nth-child(even) {
    /* background-color: rgba(29, 78, 216, 0.1); */
    background-color: rgba(231, 62, 140, 0.1);
}

.patients-table .delete-btn,
.services-table .delete-btn {
    flex: 1 1 150px;
    background: var(--danger-color);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.patients-table .delete-btn::before,
.services-table .delete-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.3), transparent 70%); /* Red radial gradient effect */
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.patients-table .delete-btn:hover,
.services-table .delete-btn:hover {
    background: #d92d2f;
    box-shadow: 0 0 15px rgba(217, 45, 47, 0.6);
}

.patients-table .delete-btn:hover::before,
.services-table .delete-btn:hover::before {
    opacity: 1;
}

.patients-table .edit-btn,
.patients-table .add-visit-btn,
.services-table .edit-btn,
.services-table .add-visit-btn {
    flex: 1 1 150px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.patients-table .edit-btn::before,
.patients-table .add-visit-btn::before,
.services-table .edit-btn::before,
.services-table .add-visit-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    /* background: radial-gradient(circle, rgba(29, 78, 216, 0.3), transparent 70%); */ /* Primary color radial gradient effect */
    background: radial-gradient(circle, rgba(231, 62, 140, 0.3), transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.patients-table .edit-btn:hover,
.patients-table .add-visit-btn:hover,
.services-table .edit-btn:hover,
.services-table .add-visit-btn:hover {
    background: var(--secondary-color); /* Darker blue for the hover effect */
    /* box-shadow: 0 0 15px rgba(0, 63, 179, 0.5); */ /* Subtle blue glow shadow */
    box-shadow: 0 0 15px rgba(231, 62, 140, 0.5);
}

.patients-table .edit-btn:hover::before,
.patients-table .add-visit-btn:hover::before,
.services-table .edit-btn:hover::before,
.services-table .add-visit-btn:hover::before {
    opacity: 1;
}

.patients-table .profile-btn {
    flex: 1 1 150px;
    background: var(--profile-color); 
    color: var(--white);
    border: none;
    padding: 10px 15px;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.patients-table .profile-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(76, 175, 80, 0.3), transparent 70%); /* Light green radial gradient effect */
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.patients-table .profile-btn:hover {
    background: #388E3C;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.patients-table .profile-btn:hover::before {
    opacity: 1;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.search-bar {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--radius);
    /* font-size: 16px; */
    font-size: large;
    transition: var(--transition);
    background: var(--white);
    color: var(--text-color);
}

.search-bar:focus {
    border-color: var(--primary-color);
    outline: none;
    /* box-shadow: 0 0 8px rgba(29, 78, 216, 0.3); */
    box-shadow: 0 0 8px rgba(231, 62, 140, 0.3);
}

.search-bar:hover {
    transform: translateY(-2px);
}


.modal {
    display: none; 
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
    z-index: 2;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(calc(-50% - .5px));
}

.modal-content {
    position: relative;
    background-color: white;
    padding: 20px;
    border-radius: var(--radius);
    max-width: calc(100% - 200px);
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    backface-visibility: hidden;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    /* font-size: 25px; */
    font-size: large;
    cursor: pointer;
}

.modal button {
    padding: 10px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background 0.3s;
}

.modal button:hover {
    background: var(--secondary-color);
}

.modal button#cancel-delete-btn {
    background: #ccc;
    margin-right: 10px;
}

.modal button#confirm-delete-btn {
    background: #e74c3c;
}

.modal form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modal form label {
    font-weight: bold;
    color: var(--primary-color);
}

.modal form input,
.modal form select,
.modal form textarea {
    padding: 10px;
    /* font-size: 18px; */
    font-size: large;
    border: 1px solid #ddd;
    border-radius: var(--radius);
    background-color: var(--white);
    color: var(--text-color);
    transition: border-color 0.3s;
}

/* Focus effect on inputs */
.modal form input:focus,
.modal form select:focus,
.modal form textarea {
    border-color: var(--primary-color);
    outline: none;
    /* box-shadow: 0 0 8px rgba(29, 78, 216, 0.3); */
    box-shadow: 0 0 8px rgba(231, 62, 140, 0.3);
}

/* Buttons */
.modal form button {
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background 0.3s;
    font-weight: bold;
}

.modal form button:hover {
    background: var(--secondary-color);
}

.modal h3 {
    text-align: center;
    margin-bottom: 20px;
}
.line {
    border: 2px solid var(--primary-color);
    border-radius: 50px;
    animation: glow 1s ease-in-out infinite alternate;
    margin-bottom: 20px;
}

#profile-details p {
    margin: 10px 0;
    /* font-size: 16px; */
    font-size: large;
}

#profile-details p strong {
    color: var(--primary-color);
}
#profile-details table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;

}

#profile-details .note-content {
    width: 100%; /* Take up the full width of the table cell */
    height: 80px; /* Fixed height for the textbox */
    overflow-y: auto; /* Allows scrolling inside the textbox if the text is too long */
    white-space: pre-wrap; /* Preserves new lines and spacing */
    word-wrap: break-word; /* Prevents text overflow */
    border-color: var(--primary-color);
    outline: none;
    /* box-shadow: 0 0 8px rgba(29, 78, 216, 0.3); */
    box-shadow: 0 0 8px rgba(231, 62, 140, 0.3);
    border-radius: 5px; /* Optional: rounded corners */
    padding: 5px;
    /* font-size: 14px; */
    font-size: large;
    resize: none; /* Prevents resizing */
}


#add-patient-btn, #add-services-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    padding: 10px 15px;
}

#add-patient-btn:hover, #add-services-btn:hover {
    background: var(--secondary-color);
}

#add-patient-btn:active, #add-services-btn:active {
    transform: scale(0.95);
}

#services-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    justify-content: center;
    /* create a box layout style */
    border: 2px solid var(--primary-color);
    border-radius: var(--radius);
    padding: 10px;
    /* background-color: rgba(29, 78, 216, 0.1); */
    background-color: rgba(231, 62, 140, 0.1);
}

.service-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.service-item input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    position: relative;
}

.service-item input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.service-item input[type="checkbox"]::before {
    content: '✔';
    /* font-size: 14px; */
    font-size: large;
    color: white;
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.service-item input[type="checkbox"]:checked::before {
    display: block;
}

#services-error {
    color: red;
    /* font-size: 14px; */
    font-size: large;
    font-weight: bold;
    margin-top: 5px;
    display: none; /* Hidden by default */
    animation: fadeIn 0.3s ease-in-out;
    justify-content: center;
    align-self: center;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

#profit h2 {
    /* font-size: 24px; */
    font-size: large;
    margin-bottom: 20px;
}

/* Container for the date inputs and filter button */
#profit > div {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
    align-items: center;
}

#profit label {
    /* font-size: 16px; */
    font-size: large;
}

#profit input[type="date"] {
    padding: 8px;
    /* font-size: 16px; */
    font-size: large;
    border: 1px solid #ccc;
    border-radius: 4px;
}

#profit button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#profit button:hover {
    background-color: #45a049;
}

/* Container for the profit cards */
#profit-cards-container {
    display: flex;
    gap: 20px;
    justify-content: space-between;
    flex-wrap: wrap; /* Allows wrapping for smaller screens */
}

/* Individual profit card styling */
.profit-card {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 30%; /* Adjust width for desktop, will wrap on smaller screens */
    text-align: center;
    transition: transform 0.3s ease;
}

.profit-card:hover {
    transform: translateY(-5px); /* Lift the card on hover */
}

/* Title styling inside the profit card */
.profit-card h3 {
    /* font-size: 20px; */
    font-size: large;
    color: #333;
    margin-bottom: 15px;
}

/* Paragraph styling inside the profit card */
.profit-card p {
    /* font-size: 18px; */
    font-size: large;
    color: #555;
}

/* Adjust card layout for small screens (mobile-first approach) */
@media screen and (max-width: 768px) {
    .profit-card {
        width: 100%; /* Cards will stack on mobile devices */
    }

    #profit-cards-container {
        flex-direction: column; /* Stack cards vertically on small screens */
    }
}

#profit-cards-container {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Floating Theme Toggle */
.floating-theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--white);
    box-shadow: var(--shadow);
    border: none;
    cursor: pointer;
    /* font-size: 24px; */
    font-size: large;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    z-index: 9999;
}

.floating-theme-toggle i {
    /* font-size: 22px; */
    font-size: large;
    transition: color 0.3s;
}

body.dark-mode .floating-theme-toggle {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
}


body.dark-mode {
    background-color: var(--dark-background-color);
    color: var(--dark-text-color);
}

body.dark-mode .main-content {
    background-color: var(--dark-background-color);
}

body.dark-mode header {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .menu-toggle {
    color: var(--primary-color);
}

body.dark-mode .sidebar {
    background: var(--dark-card-background);
}

body.dark-mode .toolbar button {
    background: var(--primary-color);
    color: var(--white);
}

body.dark-mode .toolbar button:hover {
    background: var(--secondary-color);
}

body.dark-mode .quick-access {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
    box-shadow: var(--dark-shadow);
}

body.dark-mode .card {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
    box-shadow: var(--dark-shadow);
}

body.dark-mode .card span {
    color: var(--primary-color);
}

body.dark-mode table, body.dark-mode #profile-details table {
    background: var(--dark-card-background);
    color: var(--dark-text-color);
    box-shadow: var(--dark-shadow);
}

body.dark-mode table th,
body.dark-mode table td,
#profile-details table th,
#profile-details table td {
    border-color: #444;
}

body.dark-mode .search-bar {
    background: var(--dark-card-background);
    border-color: #444;
    color: var(--dark-text-color);
}

body.dark-mode .search-bar:focus {
    border-color: var(--primary-color);
    /* box-shadow: 0 0 8px rgba(29, 78, 216, 0.5); */
    box-shadow: 0 0 8px rgba(231, 62, 140, 0.5);
    transition: var(--transition);
}

body.dark-mode .modal-content {
    background-color: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .modal button {
    background: var(--primary-color);
    color: var(--white);
}

body .dark-mode .modal button:hover {
    background: var(--secondary-color);
}

body.dark-mode .modal form input,
body.dark-mode .modal form select,
body.dark-mode .modal form textarea {
    border-color: #444;
    background-color: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .modal form input:focus,
body.dark-mode .modal form select:focus,
body.dark-mode .modal form textarea:focus {
    border-color: var(--primary-color);
    /* box-shadow: 0 0 8px rgba(29, 78, 216, 0.5); */
    box-shadow: 0 0 8px rgba(231, 62, 140, 0.5);
}

body.dark-mode .modal form button {
    background: var(--primary-color);
    color: var(--white);
}

body.dark-mode .modal form button:hover {
    background: var(--secondary-color);
}

body.dark-mode #profile-details p {
    color: var(--dark-text-color);
}

body.dark-mode #profile-details p strong {
    color: var(--primary-color);
}

body.dark-mode .note-content {
    background-color: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode #profit-cards-container {
    background-color: var(--dark-card-background);
    color: var(--dark-text-color);
}

body.dark-mode .profit-card {
    background-color: hsl(0, 0%, 20%);
    color: var(--dark-text-color);
}

body.dark-mode .profit-card h3 {
    color: var(--primary-color);
}

body.dark-mode .profit-card p {
    color: var(--dark-text-color);
}

body .dark-mode .date-filter {
    color: var(--primary-color);
}

.hidden-section {
    display: none;
}

.active-section {
    display: block;
}